"""
增强的期货技术分析引擎
结合15分钟K线和日K线数据进行综合分析
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class EnhancedAnalysisEngine:
    """增强分析引擎 - 结合多周期数据"""

    def __init__(self):
        """初始化增强分析引擎"""
        self.analysis_results = {}

    def generate_comprehensive_analysis(self, exchange, contract, minute_data, daily_data, 
                                      minute_indicators, daily_indicators):
        """生成综合分析报告"""
        analysis = {
            'exchange': exchange,
            'contract': contract,
            'timestamp': datetime.now(),
            'current_price': daily_data['close'].iloc[-1],
            'current_volume': daily_data['volume'].iloc[-1],
            'current_open_interest': daily_data['open_interest'].iloc[-1],
            'data_period': f"{daily_data['datetime'].iloc[0].strftime('%Y-%m-%d')} 至 {daily_data['datetime'].iloc[-1].strftime('%Y-%m-%d')}",
            'total_records': {
                'minute': len(minute_data),
                'daily': len(daily_data)
            }
        }

        # 各项分析
        analysis['trend'] = self.analyze_enhanced_trend(minute_indicators, daily_indicators, minute_data, daily_data)
        analysis['momentum'] = self.analyze_enhanced_momentum(minute_indicators, daily_indicators)
        analysis['volatility'] = self.analyze_enhanced_volatility(minute_indicators, daily_indicators, daily_data)
        analysis['volume'] = self.analyze_enhanced_volume(minute_indicators, daily_indicators, minute_data, daily_data)
        analysis['open_interest'] = self.analyze_enhanced_open_interest(daily_data)
        analysis['support_resistance'] = self.analyze_enhanced_support_resistance(minute_indicators, daily_indicators, minute_data, daily_data)
        analysis['overall_assessment'] = self.generate_enhanced_assessment(analysis)

        return analysis

    def analyze_enhanced_trend(self, minute_indicators, daily_indicators, minute_data, daily_data):
        """增强趋势分析 - 整体趋势基于日K线，多周期分析结合分钟和日K线"""
        minute_trend_data = minute_indicators['trend']
        daily_trend_data = daily_indicators['trend']

        # 计算多个周期的移动平均线（基于分钟K线）
        ma5 = self._calculate_ma(minute_data['close'], 5)
        ma10 = self._calculate_ma(minute_data['close'], 10)
        ma20 = self._calculate_ma(minute_data['close'], 20)
        ma60 = self._calculate_ma(minute_data['close'], 60)

        current_price = minute_data['close'].iloc[-1]

        # 移动平均线排列分析
        ma_analysis = self._analyze_ma_arrangement(current_price, ma5, ma10, ma20, ma60)

        # 整体趋势基于日K线数据
        overall_direction = self._determine_overall_trend_quality_daily(
            daily_trend_data['overall_direction'],
            ma_analysis['status']
        )

        # 趋势强度基于日K线（定量）
        daily_trend_strength = daily_trend_data['trend_strength']
        strength_level = self._classify_trend_strength(daily_trend_strength)

        # 多周期趋势方向：短期用分钟K线，中长期用日K线
        short_trend = minute_trend_data['short_trend']  # 基于分钟K线
        medium_trend = daily_trend_data['medium_trend']  # 基于日K线
        long_trend = daily_trend_data['long_trend']      # 基于日K线

        return {
            'overall_direction_qualitative': overall_direction,
            'trend_strength_quantitative': daily_trend_strength,
            'strength_level': strength_level,
            'short_term': self._describe_trend(short_trend),
            'medium_term': self._describe_trend(medium_trend),
            'long_term': self._describe_trend(long_trend),
            'ma_analysis': ma_analysis
        }

    def analyze_enhanced_momentum(self, minute_indicators, daily_indicators):
        """增强动量分析 - 综合多个指标给出定性结论"""
        # 获取各项指标
        rsi = minute_indicators.get('rsi', [])
        kdj = minute_indicators.get('kdj', {})
        macd = minute_indicators.get('macd', {})
        momentum = minute_indicators.get('momentum', {})
        
        # 分析各个指标
        rsi_signal = self._analyze_rsi_signal(rsi)
        kdj_signal = kdj.get('signal', 'neutral')
        macd_signal = macd.get('cross_signal', 'none')
        momentum_signal = momentum.get('momentum_signal', 'neutral')
        
        # 综合动量分析
        overall_momentum = self._determine_overall_momentum_quality(
            rsi_signal, kdj_signal, macd_signal, momentum_signal
        )
        
        # 特殊信号检测
        special_signals = self._detect_special_momentum_signals(rsi, kdj, macd)
        
        return {
            'overall_momentum_qualitative': overall_momentum,
            'special_signals': special_signals
        }

    def analyze_enhanced_volatility(self, minute_indicators, daily_indicators, daily_data):
        """增强波动性分析"""
        volatility_data = daily_indicators['volatility']  # 使用日线数据
        bollinger = minute_indicators['bollinger']  # 使用分钟线布林带
        
        # 基础波动性信息
        volatility_level = volatility_data['volatility_level']
        historical_vol = volatility_data['historical_volatility']
        atr_vol = volatility_data['atr_volatility']
        percentile = volatility_data['volatility_percentile']
        
        # 百分位解读
        percentile_description = self._interpret_volatility_percentile(percentile)
        
        # 止损止盈建议
        stop_loss_profit = self._calculate_stop_loss_profit_levels(daily_data, atr_vol)
        
        # 布林带位置描述
        bollinger_description = self._describe_bollinger_position(bollinger)
        
        return {
            'level': volatility_level,
            'historical_volatility': historical_vol,
            'atr_volatility': atr_vol,
            'percentile': percentile,
            'percentile_description': percentile_description,
            'stop_loss_profit': stop_loss_profit,
            'bollinger_description': bollinger_description
        }

    def analyze_enhanced_volume(self, minute_indicators, daily_indicators, minute_data, daily_data):
        """增强成交量分析 - 分别基于日K和分钟K分析"""
        daily_volume_data = daily_indicators['volume']  # 日线成交量数据
        minute_volume_data = minute_indicators['volume']  # 分钟线成交量数据

        # 日线成交量分析（中长期趋势）
        daily_volume_status = self._analyze_volume_status(daily_volume_data)
        daily_price_volume_relation = self._analyze_price_volume_relation_comprehensive(
            daily_volume_data, daily_data
        )

        # 分钟线成交量分析（短期活跃度）
        minute_volume_status = self._analyze_volume_status(minute_volume_data)
        minute_price_volume_relation = self._analyze_price_volume_relation_comprehensive(
            minute_volume_data, minute_data
        )

        return {
            'daily_analysis': {
                'status': daily_volume_status,
                'price_volume_relation': daily_price_volume_relation
            },
            'minute_analysis': {
                'status': minute_volume_status,
                'price_volume_relation': minute_price_volume_relation
            }
        }

    def analyze_enhanced_open_interest(self, daily_data):
        """增强持仓量分析"""
        if 'open_interest' not in daily_data.columns:
            return {'summary': '无持仓量数据'}

        open_interest = daily_data['open_interest'].values
        prices = daily_data['close'].values
        
        # 持仓量趋势
        oi_trend = self._analyze_oi_trend(open_interest)
        
        # 价格持仓量关系
        price_oi_relation = self._analyze_price_oi_relation(prices, open_interest)
        
        # 持仓量变化率
        oi_change_rate = self._calculate_oi_change_rate(open_interest)
        
        # 综合结论
        summary = self._generate_oi_summary(oi_trend, price_oi_relation, oi_change_rate)
        
        return {
            'summary': summary,
            'change_rate': oi_change_rate
        }

    def analyze_enhanced_support_resistance(self, minute_indicators, daily_indicators, minute_data, daily_data):
        """增强支撑压力位分析 - 最近位置基于分钟K线，最强位置基于日K线"""
        # 分钟K线数据（用于最近位置）
        minute_sr_data = minute_indicators['support_resistance']
        # 日K线数据（用于最强位置）
        daily_sr_data = daily_indicators['support_resistance']

        current_price = minute_sr_data['current_price']

        # 获取最近的压力位和支撑位（基于分钟K线）
        nearest_resistance = minute_sr_data['resistance'][0] if minute_sr_data['resistance'] else None
        nearest_support = minute_sr_data['support'][0] if minute_sr_data['support'] else None

        # 获取强度最大的压力位和支撑位（基于日K线）
        strongest_resistance = None
        strongest_support = None

        if daily_sr_data['resistance']:
            strongest_resistance = max(daily_sr_data['resistance'], key=lambda x: x['strength'])
        if daily_sr_data['support']:
            strongest_support = max(daily_sr_data['support'], key=lambda x: x['strength'])

        # 计算距离百分比
        def calc_distance(price, current):
            if price > current:
                return (price - current) / current * 100
            else:
                return (current - price) / current * 100

        # 处理压力位
        resistance_levels = {}
        if nearest_resistance:
            resistance_levels['nearest'] = {
                'price': nearest_resistance['price'],
                'distance_pct': calc_distance(nearest_resistance['price'], current_price),
                'strength': nearest_resistance['strength'],
                'source': '15分钟K线'
            }

        if strongest_resistance:
            resistance_levels['strongest'] = {
                'price': strongest_resistance['price'],
                'distance_pct': calc_distance(strongest_resistance['price'], current_price),
                'strength': strongest_resistance['strength'],
                'source': '日K线'
            }

        # 处理支撑位
        support_levels = {}
        if nearest_support:
            support_levels['nearest'] = {
                'price': nearest_support['price'],
                'distance_pct': calc_distance(nearest_support['price'], current_price),
                'strength': nearest_support['strength'],
                'source': '15分钟K线'
            }

        if strongest_support:
            support_levels['strongest'] = {
                'price': strongest_support['price'],
                'distance_pct': calc_distance(strongest_support['price'], current_price),
                'strength': strongest_support['strength'],
                'source': '日K线'
            }

        # 基于分钟K线的突破分析
        breakthrough_analysis = self._analyze_breakthrough_potential(
            minute_data, resistance_levels.get('nearest'), support_levels.get('nearest')
        )

        return {
            'current_price': current_price,
            'resistance_levels': resistance_levels,
            'support_levels': support_levels,
            'breakthrough_analysis': breakthrough_analysis
        }

    def generate_enhanced_assessment(self, analysis):
        """生成增强综合评估"""
        # 基础评分
        score = 50
        
        # 趋势评分
        trend_direction = analysis['trend']['overall_direction_qualitative']
        if '强势' in trend_direction and '上升' in trend_direction:
            score += 25
        elif '上升' in trend_direction:
            score += 15
        elif '强势' in trend_direction and '下降' in trend_direction:
            score -= 25
        elif '下降' in trend_direction:
            score -= 15
        
        # 动量评分
        momentum = analysis['momentum']['overall_momentum_qualitative']
        if '强烈看多' in momentum:
            score += 15
        elif '看多' in momentum:
            score += 10
        elif '强烈看空' in momentum:
            score -= 15
        elif '看空' in momentum:
            score -= 10
        
        # 成交量评分
        minute_volume_status = analysis['volume']['minute_analysis']['status']
        if '配合' in minute_volume_status and score > 50:
            score += 5
        elif '背离' in minute_volume_status:
            score -= 10
        
        # 确定投资建议
        recommendation = self._determine_investment_recommendation(score)
        
        # 风险分析
        risk_analysis = self._analyze_comprehensive_risk(analysis)
        
        # 关键信号
        key_signals = self._extract_key_signals(analysis)
        
        return {
            'score': max(0, min(100, score)),
            'investment_recommendation': recommendation,
            'risk_analysis': risk_analysis,
            'key_signals': key_signals
        }

    # 辅助方法
    def _calculate_ma(self, prices, period):
        """计算移动平均线"""
        return prices.rolling(window=period).mean().iloc[-1] if len(prices) >= period else None

    def _analyze_ma_arrangement(self, current_price, ma5, ma10, ma20, ma60):
        """分析移动平均线排列"""
        mas = [ma for ma in [ma5, ma10, ma20, ma60] if ma is not None]
        if len(mas) < 3:
            return {'status': '数据不足', 'description': '移动平均线数据不足'}

        # 检查多头排列
        if current_price > ma5 and ma5 > ma10 and ma10 > ma20 and ma20 > ma60:
            return {'status': '完美多头排列', 'description': '价格和各周期均线呈完美多头排列，趋势强劲'}
        elif current_price > ma5 and ma5 > ma20:
            return {'status': '多头排列', 'description': '价格位于主要均线之上，趋势向好'}
        elif current_price < ma5 and ma5 < ma10 and ma10 < ma20 and ma20 < ma60:
            return {'status': '完美空头排列', 'description': '价格和各周期均线呈完美空头排列，下跌趋势明确'}
        elif current_price < ma5 and ma5 < ma20:
            return {'status': '空头排列', 'description': '价格位于主要均线之下，趋势偏弱'}
        else:
            return {'status': '均线纠缠', 'description': '均线排列混乱，趋势不明确'}

    def _classify_trend_strength(self, strength):
        """分类趋势强度"""
        if strength > 0.8:
            return '极强'
        elif strength > 0.6:
            return '较强'
        elif strength > 0.4:
            return '中等'
        elif strength > 0.2:
            return '较弱'
        else:
            return '极弱'

    def _analyze_trend_consistency(self, short, medium, long):
        """分析趋势一致性"""
        if short > 0 and medium > 0 and long > 0:
            return {'status': '上升趋势明确', 'description': '短中长期趋势一致向上，趋势可靠性高'}
        elif short < 0 and medium < 0 and long < 0:
            return {'status': '下跌趋势明确', 'description': '短中长期趋势一致向下，下跌趋势确立'}
        elif short == 0 and medium == 0 and long == 0:
            return {'status': '震荡趋势', 'description': '各周期均为震荡，缺乏明确方向'}
        else:
            return {'status': '趋势模糊', 'description': '不同周期趋势存在分歧，震荡居多'}

    def _describe_trend(self, trend_value):
        """描述趋势方向"""
        if trend_value > 0:
            return '上升'
        elif trend_value < 0:
            return '下降'
        else:
            return '震荡'

    def _determine_overall_trend_quality_daily(self, basic_direction, ma_status):
        """确定整体趋势程度（基于日K线数据）"""
        if '强势上升' in basic_direction and '多头排列' in ma_status:
            return '强势上升趋势'
        elif '上升' in basic_direction:
            return '上升趋势'
        elif '强势下降' in basic_direction and '空头排列' in ma_status:
            return '强势下降趋势'
        elif '下降' in basic_direction:
            return '下降趋势'
        else:
            return '震荡趋势'

    def _analyze_rsi_signal(self, rsi):
        """分析RSI信号"""
        if len(rsi) == 0:
            return 'neutral'

        current_rsi = rsi[-1]
        if current_rsi > 80:
            return 'strong_sell'
        elif current_rsi > 70:
            return 'sell'
        elif current_rsi < 20:
            return 'strong_buy'
        elif current_rsi < 30:
            return 'buy'
        else:
            return 'neutral'

    def _determine_overall_momentum_quality(self, rsi_signal, kdj_signal, macd_signal, momentum_signal):
        """确定整体动量质量"""
        buy_signals = sum(1 for s in [rsi_signal, kdj_signal, macd_signal, momentum_signal]
                         if s in ['buy', 'strong_buy', 'golden_cross', 'strengthening', 'oversold'])
        sell_signals = sum(1 for s in [rsi_signal, kdj_signal, macd_signal, momentum_signal]
                          if s in ['sell', 'strong_sell', 'death_cross', 'weakening', 'overbought'])

        if buy_signals >= 3:
            return '强烈看多动量'
        elif buy_signals >= 2:
            return '看多动量'
        elif sell_signals >= 3:
            return '强烈看空动量'
        elif sell_signals >= 2:
            return '看空动量'
        else:
            return '中性动量'

    def _detect_special_momentum_signals(self, rsi, kdj, macd):
        """检测特殊动量信号"""
        signals = []

        # MACD金叉死叉
        if macd.get('cross_signal') == 'golden_cross':
            signals.append('MACD金叉信号')
        elif macd.get('cross_signal') == 'death_cross':
            signals.append('MACD死叉信号')

        # RSI极值
        if len(rsi) > 0:
            current_rsi = rsi[-1]
            if current_rsi > 85:
                signals.append('RSI严重超买')
            elif current_rsi < 15:
                signals.append('RSI严重超卖')

        # KDJ金叉死叉
        if kdj.get('signal') == 'golden_cross':
            signals.append('KDJ金叉信号')
        elif kdj.get('signal') == 'death_cross':
            signals.append('KDJ死叉信号')

        return signals if signals else ['无特殊信号']

    def _interpret_volatility_percentile(self, percentile):
        """解读波动率百分位"""
        if percentile > 80:
            return '当前波动率处于历史高位，市场情绪激烈'
        elif percentile > 60:
            return '当前波动率偏高，市场活跃度较强'
        elif percentile > 40:
            return '当前波动率处于正常水平'
        elif percentile > 20:
            return '当前波动率偏低，市场相对平静'
        else:
            return '当前波动率处于历史低位，市场非常平静'

    def _calculate_stop_loss_profit_levels(self, daily_data, atr_vol):
        """计算止损止盈建议"""
        current_price = daily_data['close'].iloc[-1]
        atr_value = current_price * atr_vol

        # 基于ATR的止损止盈
        stop_loss_long = current_price - (atr_value * 2)  # 做多止损
        stop_loss_short = current_price + (atr_value * 2)  # 做空止损
        take_profit_long = current_price + (atr_value * 3)  # 做多止盈
        take_profit_short = current_price - (atr_value * 3)  # 做空止盈

        return {
            'long_stop_loss': stop_loss_long,
            'long_take_profit': take_profit_long,
            'short_stop_loss': stop_loss_short,
            'short_take_profit': take_profit_short,
            'atr_value': atr_value
        }

    def _describe_bollinger_position(self, bollinger):
        """描述布林带位置"""
        if len(bollinger['position']) == 0:
            return '布林带数据不足'

        position = bollinger['position'][-1]
        width = bollinger['width'][-1]

        # 位置描述
        if position > 0.8:
            position_desc = '价格接近布林带上轨，可能面临回调压力'
        elif position < 0.2:
            position_desc = '价格接近布林带下轨，可能出现反弹机会'
        else:
            position_desc = '价格位于布林带中轨附近，处于震荡状态'

        # 宽度描述
        if width > 0.1:
            width_desc = '布林带较宽，市场波动性较大'
        elif width < 0.05:
            width_desc = '布林带较窄，可能酝酿突破行情'
        else:
            width_desc = '布林带宽度正常'

        return f"{position_desc}；{width_desc}"

    def _analyze_volume_status(self, volume_data):
        """分析成交量状态"""
        volume_ratio = volume_data.get('volume_ratio', 1)
        if isinstance(volume_ratio, (list, np.ndarray)) and len(volume_ratio) > 0:
            volume_ratio = volume_ratio[-1]

        if volume_ratio > 2.0:
            return '成交量大幅放大，市场活跃度极高'
        elif volume_ratio > 1.5:
            return '成交量适度放大，市场参与度提升'
        elif volume_ratio < 0.5:
            return '成交量明显萎缩，市场观望情绪浓厚'
        else:
            return '成交量处于正常水平'

    def _analyze_price_volume_relation_comprehensive(self, volume_data, daily_data):
        """综合分析价量关系"""
        # 价量背离检测
        if volume_data.get('price_volume_divergence', False):
            pv_status = '价量背离'
            pv_description = '价格与成交量走势出现背离，需要警惕趋势变化风险'
        else:
            pv_status = '价量配合'
            pv_description = '价格与成交量走势配合良好，趋势相对可靠'

        # OBV分析
        obv = volume_data.get('obv', [])
        if len(obv) >= 10:
            recent_obv = obv[-10:]
            recent_prices = daily_data['close'].iloc[-10:].values

            obv_trend = 1 if recent_obv[-1] > recent_obv[0] else -1
            price_trend = 1 if recent_prices[-1] > recent_prices[0] else -1

            if obv_trend * price_trend > 0:
                obv_conclusion = 'OBV与价格走势一致，验证当前趋势'
            else:
                obv_conclusion = 'OBV与价格走势背离，可能预示趋势转变'
        else:
            obv_conclusion = 'OBV数据不足'

        return f"{pv_status}，{pv_description}；{obv_conclusion}"

    def _analyze_oi_trend(self, open_interest):
        """分析持仓量趋势"""
        if len(open_interest) < 10:
            return {'direction': 'unknown', 'change_rate': 0}

        recent_oi = open_interest[-10:]
        oi_change = (recent_oi[-1] - recent_oi[0]) / recent_oi[0] * 100

        if oi_change > 5:
            return {'direction': '显著增长', 'change_rate': oi_change}
        elif oi_change > 2:
            return {'direction': '温和增长', 'change_rate': oi_change}
        elif oi_change < -5:
            return {'direction': '显著下降', 'change_rate': oi_change}
        elif oi_change < -2:
            return {'direction': '温和下降', 'change_rate': oi_change}
        else:
            return {'direction': '相对稳定', 'change_rate': oi_change}

    def _analyze_price_oi_relation(self, prices, open_interest):
        """分析价格持仓量关系"""
        if len(prices) < 10 or len(open_interest) < 10:
            return {'relation': '数据不足', 'signal': '无法判断'}

        price_change = (prices[-1] - prices[-10]) / prices[-10]
        oi_change = (open_interest[-1] - open_interest[-10]) / open_interest[-10]

        if price_change > 0 and oi_change > 0:
            return {'relation': '价涨量增', 'signal': '多头建仓'}
        elif price_change > 0 and oi_change < 0:
            return {'relation': '价涨量减', 'signal': '空头平仓'}
        elif price_change < 0 and oi_change > 0:
            return {'relation': '价跌量增', 'signal': '空头建仓'}
        elif price_change < 0 and oi_change < 0:
            return {'relation': '价跌量减', 'signal': '多头平仓'}
        else:
            return {'relation': '价量平衡', 'signal': '观望'}

    def _calculate_oi_change_rate(self, open_interest):
        """计算持仓量日变化率"""
        if len(open_interest) < 2:
            return 0
        return (open_interest[-1] - open_interest[-2]) / open_interest[-2] * 100

    def _generate_oi_summary(self, oi_trend, price_oi_relation, oi_change_rate):
        """生成持仓量综合结论"""
        trend_desc = oi_trend['direction']
        relation_desc = price_oi_relation['relation']
        signal_desc = price_oi_relation['signal']

        # 变化程度描述
        if abs(oi_change_rate) > 3:
            change_desc = '显著变化'
        elif abs(oi_change_rate) > 1:
            change_desc = '温和变化'
        else:
            change_desc = '变化较小'

        return f"持仓量{trend_desc}，呈现{relation_desc}格局，市场信号为{signal_desc}，日变化{change_desc}"

    def _analyze_breakthrough_potential(self, minute_data, key_resistance, key_support):
        """分析突破潜力（基于分钟K线）"""
        if len(minute_data) < 20:
            return '数据不足，无法分析突破潜力'

        current_price = minute_data['close'].iloc[-1]
        recent_high = minute_data['high'].iloc[-20:].max()
        recent_low = minute_data['low'].iloc[-20:].min()
        recent_volume = minute_data['volume'].iloc[-5:].mean()
        avg_volume = minute_data['volume'].iloc[-50:-5].mean()

        analysis_parts = []

        # 压力位突破分析
        if key_resistance:
            resistance_price = key_resistance['price']
            distance_to_resistance = (resistance_price - current_price) / current_price * 100

            if distance_to_resistance < 1:  # 距离压力位1%以内
                if recent_volume > avg_volume * 1.5:
                    analysis_parts.append(f"接近关键压力位{resistance_price:.2f}，成交量放大，突破概率较高")
                else:
                    analysis_parts.append(f"接近关键压力位{resistance_price:.2f}，但成交量不足，突破存疑")
            elif distance_to_resistance < 3:
                analysis_parts.append(f"距离关键压力位{resistance_price:.2f}较近，需关注突破情况")

        # 支撑位突破分析
        if key_support:
            support_price = key_support['price']
            distance_to_support = (current_price - support_price) / current_price * 100

            if distance_to_support < 1:  # 距离支撑位1%以内
                if recent_volume > avg_volume * 1.5:
                    analysis_parts.append(f"接近关键支撑位{support_price:.2f}，成交量放大，跌破风险较高")
                else:
                    analysis_parts.append(f"接近关键支撑位{support_price:.2f}，成交量正常，支撑有效")
            elif distance_to_support < 3:
                analysis_parts.append(f"距离关键支撑位{support_price:.2f}较近，需关注支撑效果")

        return '；'.join(analysis_parts) if analysis_parts else '当前价格距离关键支撑压力位较远，暂无突破风险'

    def _determine_investment_recommendation(self, score):
        """确定投资建议"""
        if score >= 75:
            return '强烈建议做多，趋势明确向上'
        elif score >= 60:
            return '建议谨慎做多，注意风险控制'
        elif score <= 25:
            return '强烈建议做空，趋势明确向下'
        elif score <= 40:
            return '建议谨慎做空，注意反弹风险'
        else:
            return '建议观望等待，趋势不够明确'

    def _analyze_comprehensive_risk(self, analysis):
        """分析综合风险"""
        risk_factors = []

        # 波动性风险
        volatility_level = analysis['volatility']['level']
        if volatility_level in ['极高', '较高']:
            risk_factors.append('高波动性风险')

        # 趋势一致性风险（基于多周期趋势判断）
        short_trend = analysis['trend']['short_term']
        medium_trend = analysis['trend']['medium_term']
        long_trend = analysis['trend']['long_term']

        # 如果三个周期趋势不一致，认为存在不确定性风险
        trends = [short_trend, medium_trend, long_trend]
        if len(set(trends)) > 1:  # 趋势方向不一致
            risk_factors.append('趋势不确定性风险')

        # 价量背离风险
        daily_volume = analysis['volume']['daily_analysis']['price_volume_relation']
        minute_volume = analysis['volume']['minute_analysis']['price_volume_relation']
        if '背离' in daily_volume or '背离' in minute_volume:
            risk_factors.append('价量背离风险')

        # 支撑压力位风险（选择最近且最重要的位置）
        sr = analysis['support_resistance']
        resistance_levels = sr['resistance_levels']
        support_levels = sr['support_levels']

        # 找出最近的关键位置
        nearest_key_level = None
        nearest_distance = float('inf')

        if resistance_levels.get('nearest') and resistance_levels['nearest']['distance_pct'] < 3:
            resistance = resistance_levels['nearest']
            if resistance['distance_pct'] < nearest_distance:
                nearest_distance = resistance['distance_pct']
                nearest_key_level = ('resistance', resistance)

        if support_levels.get('nearest') and support_levels['nearest']['distance_pct'] < 3:
            support = support_levels['nearest']
            if support['distance_pct'] < nearest_distance:
                nearest_distance = support['distance_pct']
                nearest_key_level = ('support', support)

        # 只提示最近的关键位置风险
        if nearest_key_level:
            level_type, level_data = nearest_key_level
            if level_type == 'resistance':
                risk_factors.append('接近关键压力位风险')
            else:
                risk_factors.append('接近关键支撑位风险')

        return risk_factors if risk_factors else ['风险相对可控']

    def _extract_key_signals(self, analysis):
        """提取关键信号"""
        signals = []

        # 趋势信号
        trend_quality = analysis['trend']['overall_direction_qualitative']
        if '强势' in trend_quality:
            signals.append(f"趋势信号：{trend_quality}")

        # 动量信号
        momentum_signals = analysis['momentum']['special_signals']
        if momentum_signals != ['无特殊信号']:
            signals.extend([f"动量信号：{signal}" for signal in momentum_signals])

        # 突破信号
        breakthrough = analysis['support_resistance']['breakthrough_analysis']
        if '突破' in breakthrough or '跌破' in breakthrough:
            signals.append(f"突破信号：{breakthrough}")

        return signals if signals else ['暂无明显关键信号']
