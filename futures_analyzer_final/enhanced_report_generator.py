"""
增强的报告生成器
生成结合15分钟K线和日K线的综合分析报告
"""

import os
from datetime import datetime

class EnhancedReportGenerator:
    """增强报告生成器"""
    
    def __init__(self, output_dir="analysis_results"):
        """初始化报告生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def save_comprehensive_report(self, analysis, filename=None):
        """保存综合分析报告"""
        if filename is None:
            timestamp = analysis['timestamp'].strftime('%Y%m%d_%H%M%S')
            filename = f"comprehensive_analysis_{analysis['exchange']}_{analysis['contract']}_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入报告头部
            self._write_report_header(f, analysis)
            
            # 写入各项分析
            self._write_trend_analysis(f, analysis['trend'])
            self._write_momentum_analysis(f, analysis['momentum'])
            self._write_volatility_analysis(f, analysis['volatility'])
            self._write_volume_analysis(f, analysis['volume'])
            self._write_open_interest_analysis(f, analysis['open_interest'])
            self._write_support_resistance_analysis(f, analysis['support_resistance'])
            self._write_overall_assessment(f, analysis['overall_assessment'])
        
        return filepath
    
    def _write_report_header(self, f, analysis):
        """写入报告头部"""
        f.write("期货技术分析综合报告 v0.3\n")
        f.write("="*100 + "\n")
        f.write(f"合约代码: {analysis['exchange']}/{analysis['contract']}\n")
        f.write(f"分析时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"当前价格: {analysis['current_price']:.2f}\n")
        f.write("="*100 + "\n\n")
    
    def _write_trend_analysis(self, f, trend):
        """写入趋势分析"""
        f.write("📈 趋势分析 (基于15分钟K线)\n")
        f.write("-"*60 + "\n")
        
        # 整体趋势程度（定性）
        f.write(f"整体趋势: {trend['overall_direction_qualitative']}\n")
        
        # 趋势强度（定量）
        f.write(f"趋势强度: {trend['trend_strength_quantitative']:.3f} ({trend['strength_level']})\n")
        
        # 多周期趋势方向
        f.write(f"\n多周期趋势方向:\n")
        f.write(f"  短期趋势(5期): {trend['short_term']}\n")
        f.write(f"  中期趋势(20期): {trend['medium_term']}\n")
        f.write(f"  长期趋势(60期): {trend['long_term']}\n")
        
        # 趋势一致性
        f.write(f"\n趋势一致性: {trend['consistency']['status']}\n")
        f.write(f"一致性描述: {trend['consistency']['description']}\n")
        
        # 移动平均线分析
        ma = trend['ma_analysis']
        f.write(f"\n移动平均线分析: {ma['status']}\n")
        f.write(f"均线描述: {ma['description']}\n")
        f.write("\n")
    
    def _write_momentum_analysis(self, f, momentum):
        """写入动量分析"""
        f.write("⚡ 动量分析 (基于15分钟K线)\n")
        f.write("-"*60 + "\n")
        
        # 整体动量定性结果
        f.write(f"整体动量: {momentum['overall_momentum_qualitative']}\n")
        
        # 特殊信号
        f.write(f"\n特殊信号:\n")
        for signal in momentum['special_signals']:
            f.write(f"  • {signal}\n")
        f.write("\n")
    
    def _write_volatility_analysis(self, f, volatility):
        """写入波动性分析"""
        f.write("📊 波动性分析 (基于日K线)\n")
        f.write("-"*60 + "\n")
        
        # 基础波动性信息
        f.write(f"波动性等级: {volatility['level']}\n")
        f.write(f"历史波动率: {volatility['historical_volatility']:.4f}\n")
        f.write(f"ATR波动率: {volatility['atr_volatility']:.4f}\n")
        f.write(f"波动率百分位: {volatility['percentile']:.1f}%\n")
        
        # 百分位解读
        f.write(f"百分位解读: {volatility['percentile_description']}\n")
        
        # 止损止盈建议
        stop_profit = volatility['stop_loss_profit']
        f.write(f"\n止损止盈建议 (基于ATR):\n")
        f.write(f"  做多止损: {stop_profit['long_stop_loss']:.2f}\n")
        f.write(f"  做多止盈: {stop_profit['long_take_profit']:.2f}\n")
        f.write(f"  做空止损: {stop_profit['short_stop_loss']:.2f}\n")
        f.write(f"  做空止盈: {stop_profit['short_take_profit']:.2f}\n")
        f.write(f"  ATR数值: {stop_profit['atr_value']:.2f}\n")
        
        # 布林带描述
        f.write(f"\n布林带分析: {volatility['bollinger_description']}\n")
        f.write("\n")
    
    def _write_volume_analysis(self, f, volume):
        """写入成交量分析"""
        f.write("📦 成交量分析\n")
        f.write("-"*60 + "\n")

        # 日线成交量分析
        daily_analysis = volume['daily_analysis']
        f.write("日K线成交量分析 (中长期趋势):\n")
        f.write(f"  成交量状态: {daily_analysis['status']}\n")
        f.write(f"  价量关系: {daily_analysis['price_volume_relation']}\n")

        # 分钟线成交量分析
        minute_analysis = volume['minute_analysis']
        f.write("\n15分钟K线成交量分析 (短期活跃度):\n")
        f.write(f"  成交量状态: {minute_analysis['status']}\n")
        f.write(f"  价量关系: {minute_analysis['price_volume_relation']}\n")
        f.write("\n")
    
    def _write_open_interest_analysis(self, f, open_interest):
        """写入持仓量分析"""
        f.write("📊 持仓量分析 (基于日K线)\n")
        f.write("-"*60 + "\n")
        
        # 综合结论
        f.write(f"持仓量分析: {open_interest['summary']}\n")
        
        # 变化率
        if 'change_rate' in open_interest:
            f.write(f"日变化率: {open_interest['change_rate']:.2f}%\n")
        f.write("\n")
    
    def _write_support_resistance_analysis(self, f, sr):
        """写入支撑压力位分析"""
        f.write("📍 支撑压力位分析 (基于15分钟K线)\n")
        f.write("-"*60 + "\n")

        f.write(f"当前价格: {sr['current_price']:.2f}\n")

        # 压力位分析
        resistance_levels = sr['resistance_levels']
        f.write(f"\n压力位分析:\n")

        if resistance_levels.get('nearest'):
            nearest_r = resistance_levels['nearest']
            f.write(f"  最近压力位: {nearest_r['price']:.2f} (距离: {nearest_r['distance_pct']:.1f}%, 强度: {nearest_r['strength']})\n")
        else:
            f.write(f"  最近压力位: 暂无明显压力位\n")

        if resistance_levels.get('strongest'):
            strongest_r = resistance_levels['strongest']
            f.write(f"  最强压力位: {strongest_r['price']:.2f} (距离: {strongest_r['distance_pct']:.1f}%, 强度: {strongest_r['strength']})\n")
        else:
            f.write(f"  最强压力位: 暂无明显压力位\n")

        # 支撑位分析
        support_levels = sr['support_levels']
        f.write(f"\n支撑位分析:\n")

        if support_levels.get('nearest'):
            nearest_s = support_levels['nearest']
            f.write(f"  最近支撑位: {nearest_s['price']:.2f} (距离: {nearest_s['distance_pct']:.1f}%, 强度: {nearest_s['strength']})\n")
        else:
            f.write(f"  最近支撑位: 暂无明显支撑位\n")

        if support_levels.get('strongest'):
            strongest_s = support_levels['strongest']
            f.write(f"  最强支撑位: {strongest_s['price']:.2f} (距离: {strongest_s['distance_pct']:.1f}%, 强度: {strongest_s['strength']})\n")
        else:
            f.write(f"  最强支撑位: 暂无明显支撑位\n")

        # 突破分析
        f.write(f"\n突破分析: {sr['breakthrough_analysis']}\n")
        f.write("\n")
    
    def _write_overall_assessment(self, f, assessment):
        """写入综合评估"""
        f.write("🎯 综合评估\n")
        f.write("-"*60 + "\n")
        
        # 综合评分
        f.write(f"综合评分: {assessment['score']}/100\n")
        
        # 投资建议
        f.write(f"投资建议: {assessment['investment_recommendation']}\n")
        
        # 风险分析
        f.write(f"\n风险分析:\n")
        for risk in assessment['risk_analysis']:
            f.write(f"  • {risk}\n")
        
        # 关键信号
        f.write(f"\n关键信号:\n")
        for signal in assessment['key_signals']:
            f.write(f"  • {signal}\n")
        
        f.write("\n" + "="*100 + "\n")
        f.write("报告生成完成\n")
        f.write("注意：本报告仅供参考，投资有风险，决策需谨慎\n")
        f.write("="*100 + "\n")
