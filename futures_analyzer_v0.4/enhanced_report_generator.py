"""
增强的报告生成器
生成结合15分钟K线和日K线的综合分析报告
"""

import os
from datetime import datetime

class EnhancedReportGenerator:
    """增强报告生成器"""
    
    def __init__(self, output_dir="analysis_results"):
        """初始化报告生成器"""
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def save_comprehensive_report(self, analysis, filename=None):
        """保存综合分析报告"""
        if filename is None:
            timestamp = analysis['timestamp'].strftime('%Y%m%d_%H%M%S')
            filename = f"comprehensive_analysis_{analysis['exchange']}_{analysis['contract']}_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入报告头部
            self._write_report_header(f, analysis)
            
            # 写入各项分析
            self._write_trend_analysis(f, analysis['trend'])
            self._write_momentum_analysis(f, analysis['momentum'])
            self._write_volatility_analysis(f, analysis['volatility'])
            self._write_volume_analysis(f, analysis['volume'])
            self._write_open_interest_analysis(f, analysis['open_interest'])
            self._write_support_resistance_analysis(f, analysis['support_resistance'])
            self._write_overall_assessment(f, analysis['overall_assessment'], analysis['trend'], analysis['volatility'])
        
        return filepath
    
    def _write_report_header(self, f, analysis):
        """写入报告头部"""
        f.write("期货技术分析综合报告 v0.3\n")
        f.write("="*100 + "\n")
        f.write(f"合约代码: {analysis['contract']}\n")
        f.write(f"分析时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*100 + "\n\n")
    
    def _write_trend_analysis(self, f, trend):
        """写入趋势分析"""
        f.write("📈 趋势分析\n")
        f.write("-"*60 + "\n")

        # 整体趋势程度（基于日K线）
        trend_strength_value = trend['trend_strength_quantitative']
        f.write(f"整体趋势: {trend['overall_direction_qualitative']} (强度: {trend_strength_value:.3f})\n")
        strength_explanation = self._explain_trend_strength_value(trend_strength_value)
        f.write(f"趋势强度说明: 数值范围0-1，当前{trend_strength_value:.3f}表示{strength_explanation}\n")

        # 多周期趋势方向
        f.write(f"\n多周期趋势方向:\n")
        f.write(f"  短期趋势: {trend['short_term']} (基于15分钟K线，5个数据点)\n")
        f.write(f"  中期趋势: {trend['medium_term']} (基于日K线，20个交易日)\n")
        f.write(f"  长期趋势: {trend['long_term']} (基于日K线，60个交易日)\n")
        f.write("\n")
    
    def _write_momentum_analysis(self, f, momentum):
        """写入动量分析"""
        f.write("⚡ 动量分析 (基于日K线)\n")
        f.write("-"*60 + "\n")

        # 整体动量定性结果（通俗解释）
        momentum_explanation = self._explain_momentum(momentum['overall_momentum_qualitative'])
        f.write(f"市场动量状态: {momentum_explanation}\n")

        # 特殊信号（只在有信号时显示）
        if momentum['special_signals'] != ['无特殊信号']:
            f.write(f"\n重要信号提示:\n")
            for signal in momentum['special_signals']:
                signal_explanation = self._explain_momentum_signal(signal)
                f.write(f"  • {signal_explanation}\n")

        # 一致性说明（如果存在）
        if 'consistency_note' in momentum:
            f.write(f"\n一致性说明: {momentum['consistency_note']}\n")

        f.write("\n")
    
    def _write_volatility_analysis(self, f, volatility):
        """写入波动性分析"""
        f.write("📊 波动性分析 (基于日K线)\n")
        f.write("-"*60 + "\n")

        # 基础波动性信息（避免矛盾表述）
        f.write(f"当前波动率: {volatility['historical_volatility']:.4f} (基于日K线过去60个交易日计算)\n")
        f.write(f"ATR波动率: {volatility['atr_volatility']:.4f} (基于日K线过去14个交易日平均真实波幅)\n")
        f.write(f"波动率百分位: {volatility['percentile']:.1f}% (相对过去252个交易日历史分布)\n")

        # 综合波动性评价（避免绝对值与相对位置的矛盾）
        percentile_desc = self._get_unified_volatility_description(volatility['level'], volatility['percentile'])
        f.write(f"波动性评价: {percentile_desc}\n")

        # 止损止盈建议
        stop_profit = volatility['stop_loss_profit']
        f.write(f"\n风险控制建议 (基于ATR波动幅度):\n")
        f.write(f"  做多时建议止损价: {stop_profit['long_stop_loss']:.2f}\n")
        f.write(f"  做多时建议止盈价: {stop_profit['long_take_profit']:.2f}\n")
        f.write(f"  做空时建议止损价: {stop_profit['short_stop_loss']:.2f}\n")
        f.write(f"  做空时建议止盈价: {stop_profit['short_take_profit']:.2f}\n")
        f.write(f"  日均波动幅度: {stop_profit['atr_value']:.2f}点 (基于日K线过去14个交易日ATR计算)\n")
        f.write("\n")
    
    def _write_volume_analysis(self, f, volume):
        """写入成交量分析"""
        f.write("📦 成交量分析 (基于15分钟K线)\n")
        f.write("-"*60 + "\n")

        # 只使用分钟线成交量分析
        minute_analysis = volume['minute_analysis']
        volume_explanation = self._explain_volume_status(minute_analysis['status'])
        f.write(f"成交量状态: {volume_explanation}\n")

        # 价量关系分析（包含OBV解释）
        pv_explanation = self._explain_price_volume_relation(minute_analysis['price_volume_relation'])
        f.write(f"价量关系分析: {pv_explanation}\n")
        f.write("\n")
    
    def _write_open_interest_analysis(self, f, open_interest):
        """写入持仓量分析"""
        f.write("📊 持仓量分析 (基于日K线)\n")
        f.write("-"*60 + "\n")
        
        # 综合结论
        f.write(f"持仓量分析: {open_interest['summary']}\n")
        
        # 变化率
        if 'change_rate' in open_interest:
            f.write(f"日变化率: {open_interest['change_rate']:.2f}% (相对前一交易日)\n")
        f.write("\n")
    
    def _write_support_resistance_analysis(self, f, sr):
        """写入支撑压力位分析"""
        f.write("📍 支撑压力位分析\n")
        f.write("-"*60 + "\n")

        f.write(f"当前价格: {sr['current_price']:.2f}\n")

        # 压力位分析
        resistance_levels = sr['resistance_levels']
        f.write(f"\n压力位分析:\n")

        if resistance_levels.get('nearest'):
            nearest_r = resistance_levels['nearest']
            source = nearest_r.get('source', '15分钟K线')
            f.write(f"  最近压力位: {nearest_r['price']:.2f} (距离: {nearest_r['distance_pct']:.1f}%, 强度: {nearest_r['strength']})\n")
            f.write(f"    数据来源: 基于{source}，窗口20期，分析范围60期\n")
        else:
            f.write(f"  最近压力位: 暂无明显压力位\n")

        if resistance_levels.get('strongest'):
            strongest_r = resistance_levels['strongest']
            source = strongest_r.get('source', '日K线')
            f.write(f"  最强压力位: {strongest_r['price']:.2f} (距离: {strongest_r['distance_pct']:.1f}%, 强度: {strongest_r['strength']})\n")
            f.write(f"    数据来源: 基于{source}，窗口20期，分析范围60期\n")
        else:
            f.write(f"  最强压力位: 暂无明显压力位\n")

        # 支撑位分析
        support_levels = sr['support_levels']
        f.write(f"\n支撑位分析:\n")

        if support_levels.get('nearest'):
            nearest_s = support_levels['nearest']
            source = nearest_s.get('source', '15分钟K线')
            f.write(f"  最近支撑位: {nearest_s['price']:.2f} (距离: {nearest_s['distance_pct']:.1f}%, 强度: {nearest_s['strength']})\n")
            f.write(f"    数据来源: 基于{source}，窗口20期，分析范围60期\n")
        else:
            f.write(f"  最近支撑位: 暂无明显支撑位\n")

        if support_levels.get('strongest'):
            strongest_s = support_levels['strongest']
            source = strongest_s.get('source', '日K线')
            f.write(f"  最强支撑位: {strongest_s['price']:.2f} (距离: {strongest_s['distance_pct']:.1f}%, 强度: {strongest_s['strength']})\n")
            f.write(f"    数据来源: 基于{source}，窗口20期，分析范围60期\n")
        else:
            f.write(f"  最强支撑位: 暂无明显支撑位\n")

        # 突破分析
        f.write(f"\n突破分析: {sr['breakthrough_analysis']}\n")
        f.write("\n")
    
    def _write_overall_assessment(self, f, assessment, trend, volatility):
        """写入综合评估"""
        f.write("🎯 综合评估\n")
        f.write("-"*60 + "\n")

        # 融合移动平均线和布林带分析作为综合行情评论
        ma = trend['ma_analysis']
        ma_explanation = self._explain_ma_analysis(ma['status'])
        bollinger_explanation = self._explain_bollinger_analysis(volatility['bollinger_description'])

        # 综合行情评论和分析结果合并
        f.write(f"综合行情评论: {ma_explanation}；{bollinger_explanation}\n")
        f.write(f"综合分析结果: {assessment['investment_recommendation']}\n")

        # 风险提示
        f.write(f"\n风险提示:\n")
        for risk in assessment['risk_analysis']:
            risk_explanation = self._explain_risk(risk)
            f.write(f"  • {risk_explanation}\n")

        # 关键信号
        f.write(f"\n关键信号:\n")
        for signal in assessment['key_signals']:
            f.write(f"  • {signal}\n")

        f.write("\n" + "="*100 + "\n")
        f.write("报告生成完成\n")
        f.write("注意：本报告仅供参考，投资有风险，决策需谨慎\n")
        f.write("="*100 + "\n")

    def _explain_trend_strength(self, strength_level):
        """解释趋势强度含义"""
        explanations = {
            '极强': '趋势非常明确，价格变化方向性很强',
            '较强': '趋势比较明确，价格有较强的方向性',
            '中等': '趋势一般，价格方向性不够明确',
            '较弱': '趋势较弱，价格变化缺乏明确方向',
            '极弱': '几乎没有趋势，价格主要是震荡'
        }
        return explanations.get(strength_level, '趋势强度未知')

    def _explain_momentum(self, momentum_type):
        """解释动量含义"""
        explanations = {
            '强烈看多动量': '多个技术指标都显示买入信号，价格有较强的上涨动力',
            '看多动量': '技术指标偏向买入，价格有一定上涨动力',
            '中性动量': '技术指标没有明确的买卖信号，价格缺乏明确的上涨或下跌动力',
            '看空动量': '技术指标偏向卖出，价格有一定下跌压力',
            '强烈看空动量': '多个技术指标都显示卖出信号，价格有较强的下跌压力'
        }
        return explanations.get(momentum_type, '动量状态未知')

    def _explain_obv(self, obv_description):
        """解释OBV含义"""
        if 'OBV与价格走势一致' in obv_description:
            return obv_description + '。OBV是成交量能量指标，当价格上涨时成交量增加、价格下跌时成交量减少，说明资金流向与价格变化方向一致，验证了当前趋势的可靠性'
        elif 'OBV与价格走势背离' in obv_description:
            return obv_description + '。这意味着价格上涨时成交量在减少，或价格下跌时成交量在增加，资金流向与价格变化不一致，可能预示趋势即将转变'
        else:
            return obv_description

    def _explain_momentum_signal(self, signal):
        """解释动量信号含义"""
        explanations = {
            'MACD金叉信号': 'MACD金叉表示短期均线上穿长期均线，通常是买入信号',
            'MACD死叉信号': 'MACD死叉表示短期均线下穿长期均线，通常是卖出信号',
            'RSI严重超买': 'RSI指标显示价格已被过度买入，可能面临回调风险',
            'RSI严重超卖': 'RSI指标显示价格已被过度卖出，可能出现反弹机会',
            'KDJ金叉信号': 'KDJ金叉表示短期动量转强，可能是买入时机',
            'KDJ死叉信号': 'KDJ死叉表示短期动量转弱，可能是卖出时机'
        }
        return explanations.get(signal, signal)

    def _explain_volume_status(self, status):
        """解释成交量状态"""
        if '大幅放大' in status:
            return status + '，说明市场参与者非常活跃，可能有重要消息或事件推动'
        elif '适度放大' in status:
            return status + '，说明市场参与度有所提升，投资者关注度增加'
        elif '萎缩' in status:
            return status + '，说明市场参与者较少，投资者多持观望态度'
        else:
            return status + '，市场参与度处于常规水平'

    def _explain_price_volume_relation(self, relation):
        """解释价量关系"""
        if 'OBV与价格走势一致' in relation:
            return relation + '。OBV是成交量能量指标，当价格上涨时成交量增加、价格下跌时成交量减少，说明资金流向与价格变化方向一致，验证了当前趋势的可靠性'
        elif 'OBV与价格走势背离' in relation:
            return relation + '。这意味着价格上涨时成交量在减少，或价格下跌时成交量在增加，资金流向与价格变化不一致，可能预示趋势即将转变'
        else:
            return relation

    def _explain_ma_analysis(self, ma_status):
        """解释移动平均线分析"""
        explanations = {
            '完美多头排列': '各周期均线呈完美多头排列，价格位于所有均线之上，上涨趋势非常强劲',
            '多头排列': '价格位于主要均线之上，显示上涨趋势，但需要关注均线支撑',
            '完美空头排列': '各周期均线呈完美空头排列，价格位于所有均线之下，下跌趋势明确',
            '空头排列': '价格位于主要均线之下，显示下跌趋势，均线形成压力',
            '均线纠缠': '各周期均线相互缠绕，价格在均线附近震荡，方向不明确'
        }
        return explanations.get(ma_status, ma_status)

    def _explain_bollinger_analysis(self, bollinger_desc):
        """解释布林带分析"""
        if '接近上轨' in bollinger_desc:
            return bollinger_desc + '。布林带上轨通常是阻力位，价格接近时要警惕回调风险'
        elif '接近下轨' in bollinger_desc:
            return bollinger_desc + '。布林带下轨通常是支撑位，价格接近时可能出现反弹'
        elif '中轨附近' in bollinger_desc:
            return bollinger_desc + '。价格在布林带中轨附近通常表示震荡状态'
        else:
            return bollinger_desc

    def _explain_risk(self, risk):
        """解释风险含义"""
        explanations = {
            '高波动性风险': '市场波动较大，价格变化幅度可能超出预期，建议控制仓位',
            '趋势不确定性风险': '市场方向不明确，可能出现反复震荡，不适合追涨杀跌',
            '价量背离风险': '价格与成交量走势不一致，可能预示趋势即将转变',
            '接近关键压力位风险': '价格接近重要阻力位，可能面临回调压力',
            '接近关键支撑位风险': '价格接近重要支撑位，需要关注是否会跌破支撑'
        }
        return explanations.get(risk, risk)

    def _explain_volatility_level(self, level):
        """解释波动性等级"""
        explanations = {
            '极高': '市场波动非常剧烈，价格变化幅度很大，风险和机会并存',
            '较高': '市场波动较大，价格变化明显，需要注意风险控制',
            '正常': '市场波动处于正常水平，价格变化相对稳定',
            '较低': '市场波动较小，价格变化平缓，适合稳健投资',
            '极低': '市场波动很小，价格变化微弱，可能酝酿大的变化'
        }
        return explanations.get(level, level)

    def _get_unified_volatility_description(self, level, percentile):
        """统一波动性描述，避免矛盾"""
        if percentile > 80:
            if level in ['极高', '较高']:
                return f"当前波动率处于历史高位({percentile:.1f}%)，市场波动剧烈"
            else:
                return f"虽然绝对波动率{level}，但相对历史处于高位({percentile:.1f}%)，需关注波动风险"
        elif percentile > 60:
            return f"当前波动率处于历史中高位({percentile:.1f}%)，市场活跃度较强"
        elif percentile > 40:
            return f"当前波动率处于历史中等水平({percentile:.1f}%)，市场波动正常"
        elif percentile > 20:
            return f"当前波动率处于历史中低位({percentile:.1f}%)，市场相对平静"
        else:
            return f"当前波动率处于历史低位({percentile:.1f}%)，市场非常平静"

    def _explain_trend_strength_value(self, value):
        """解释趋势强度数值含义"""
        if value > 0.8:
            return "极强趋势，价格方向性非常明确"
        elif value > 0.6:
            return "较强趋势，价格有明确的方向性"
        elif value > 0.4:
            return "中等趋势，价格方向性一般"
        elif value > 0.2:
            return "较弱趋势，价格方向性不够明确"
        else:
            return "极弱趋势，价格主要呈震荡状态"
